{"name": "digital-wallet-api-ass5", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only ./src/server.ts", "build": "tsc", "vercel-build": "npm run build", "lint": "npx eslint ./src"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "eslint": "^9.32.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "ms": "^2.1.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "@types/ms": "^2.1.0", "@types/node": "^24.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}